import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "CliniX Medical Centre | Family Medicine & Healthcare Services in Calgary",
  description: "CliniX Medical Centre provides comprehensive family medicine and healthcare services in Calgary. Located at 1029 17 Ave SW. Call (************* to book your appointment.",
  keywords: "medical centre, family doctor, healthcare, Calgary, medical clinic, family medicine, walk-in clinic",
  authors: [{ name: "CliniX Medical Centre" }],
  creator: "CliniX Medical Centre",
  publisher: "CliniX Medical Centre",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://theclinix.ca'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "CliniX Medical Centre | Family Medicine & Healthcare Services in Calgary",
    description: "CliniX Medical Centre provides comprehensive family medicine and healthcare services in Calgary. Located at 1029 17 Ave SW. Call (************* to book your appointment.",
    url: 'https://theclinix.ca',
    siteName: 'CliniX Medical Centre',
    images: [
      {
        url: '/CliniX-Logo.png',
        width: 1200,
        height: 630,
        alt: 'CliniX Medical Centre Logo',
      },
    ],
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "CliniX Medical Centre | Family Medicine & Healthcare Services in Calgary",
    description: "CliniX Medical Centre provides comprehensive family medicine and healthcare services in Calgary. Located at 1029 17 Ave SW. Call (************* to book your appointment.",
    images: ['/CliniX-Logo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "MedicalOrganization",
              "name": "CliniX Medical Centre",
              "description": "Family medicine and healthcare services in Calgary",
              "url": "https://theclinix.ca",
              "logo": "https://theclinix.ca/CliniX-Logo.png",
              "image": "https://theclinix.ca/CliniX-Logo.png",
              "telephone": "+14035450369",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "1029 17 Ave SW #200",
                "addressLocality": "Calgary",
                "addressRegion": "AB",
                "postalCode": "T2T 0A9",
                "addressCountry": "CA"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "51.0375",
                "longitude": "-114.0719"
              },
              "openingHours": [
                "Mo-We 17:00-19:00",
                "Th 09:00-17:00"
              ],
              "priceRange": "$$",
              "medicalSpecialty": "Family Medicine"
            })
          }}
        />
      </head>
      <body
        className={`${inter.variable} ${poppins.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
