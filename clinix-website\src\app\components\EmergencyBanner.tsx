'use client';

import { useState } from 'react';
import { FiX, FiAlertTriangle } from 'react-icons/fi';

export default function EmergencyBanner() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 relative animate-slide-in-top">
      <div className="container-custom flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FiAlertTriangle className="w-5 h-5 flex-shrink-0 animate-pulse" />
          <span className="font-semibold text-sm lg:text-base">
            Medical Emergency? Call 911 immediately | Health Link: 811
          </span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-white hover:text-red-200 transition-colors p-1 hover:bg-white/10 rounded-full"
          aria-label="Close emergency banner"
        >
          <FiX className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}
