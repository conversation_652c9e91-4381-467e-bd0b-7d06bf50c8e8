# CliniX Medical Centre Website

A modern, professional website for CliniX Medical Centre - a family medicine practice located in Calgary, Alberta.

## 🏥 About CliniX Medical Centre

CliniX Medical Centre provides comprehensive healthcare services to the Calgary community, offering:

- Family Medicine
- Walk-in Clinic Services
- Vaccinations
- Health Assessments
- Specialized Care
- Wellness Programs

**Location:** 1029 17 Ave SW #200, Calgary, AB T2T 0A9
**Phone:** (*************
**Email:** <EMAIL>

## 🚀 Features

- **Modern Design:** Clean, professional medical website design
- **Responsive:** Fully responsive design that works on all devices
- **SEO Optimized:** Comprehensive SEO with structured data and meta tags
- **Accessibility:** WCAG compliant with proper focus management
- **Performance:** Optimized images and fast loading times
- **Interactive Chat:** Customer service chat widget
- **Emergency Banner:** Prominent emergency contact information
- **Google Maps Integration:** Easy-to-find location with directions

## 🛠 Technology Stack

- **Framework:** Next.js 15.3.3 with App Router
- **Styling:** Tailwind CSS 4.0
- **Icons:** React Icons (Feather Icons)
- **Fonts:** Inter & Poppins from Google Fonts
- **Language:** TypeScript
- **Deployment Ready:** Optimized for Vercel deployment

## 📱 Components

- `Header` - Navigation with contact info and mobile menu
- `Hero` - Main landing section with call-to-action
- `Services` - Medical services grid
- `About` - Mission, values, and team information
- `Testimonials` - Patient reviews and feedback
- `Contact` - Location, hours, and contact information
- `Footer` - Site links and business information
- `ChatWidget` - Interactive customer support
- `EmergencyBanner` - Emergency contact information

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
clinix-website/
├── src/
│   └── app/
│       ├── components/          # Reusable React components
│       ├── globals.css         # Global styles and Tailwind
│       ├── layout.tsx          # Root layout with SEO
│       ├── page.tsx            # Homepage
│       ├── loading.tsx         # Loading component
│       ├── not-found.tsx       # 404 error page
│       └── sitemap.ts          # SEO sitemap
├── public/
│   ├── CliniX-Logo.png        # Company logo
│   └── robots.txt             # SEO robots file
└── package.json
```

## 🎨 Design Features

- **Color Scheme:** Professional medical blue (#0066cc) with clean whites and grays
- **Typography:** Inter for body text, Poppins for headings
- **Layout:** Modern grid-based layout with proper spacing
- **Animations:** Subtle hover effects and transitions
- **Accessibility:** High contrast ratios and keyboard navigation

## 📞 Contact Information

- **Phone:** (*************
- **Email:** <EMAIL>
- **Address:** 1029 17 Ave SW #200, Calgary, AB T2T 0A9

## 🕒 Business Hours

- **Monday - Wednesday:** 5:00 PM - 7:00 PM
- **Thursday:** 9:00 AM - 5:00 PM
- **Friday - Sunday:** Closed

## 🚨 Emergency Information

For medical emergencies, call 911 immediately or visit your nearest emergency room.
For health advice, call Health Link at 811.

## 📈 SEO Features

- Structured data for medical organization
- Open Graph and Twitter Card meta tags
- Sitemap and robots.txt
- Local business schema markup
- Optimized meta descriptions and titles

## 🚀 Deployment

This project is optimized for deployment on Vercel:

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

## 📄 License

This project is proprietary to CliniX Medical Centre.
