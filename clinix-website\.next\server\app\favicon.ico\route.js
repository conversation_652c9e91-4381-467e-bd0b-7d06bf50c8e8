"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_C_3A_5CUsers_5Cgtmot_5CDocuments_5CGitHub_5CCliniX_Website_5Cclinix_website_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_C_3A_5CUsers_5Cgtmot_5CDocuments_5CGitHub_5CCliniX_Website_5Cclinix_website_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"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\", 'base64'\n  )\n\nif (false) {}\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\n");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CCliniX-Website%5Cclinix-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();