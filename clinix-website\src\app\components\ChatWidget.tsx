'use client';

import { useState } from 'react';
import { FiMessageCircle, FiX, FiSend, FiPhone } from 'react-icons/fi';

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      // In a real implementation, this would send the message to a chat service
      // For now, we'll just redirect to phone call
      window.location.href = 'tel:+14035450369';
    }
  };

  const quickMessages = [
    "I'd like to book an appointment",
    "What are your hours?",
    "Do you accept walk-ins?",
    "What services do you offer?"
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Chat Button */}
      {!isOpen && (
        <button
          onClick={toggleChat}
          className="bg-gradient-to-r from-primary to-primary-light text-white p-5 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-110 group animate-bounce-in"
          aria-label="Open chat"
        >
          <FiMessageCircle className="w-7 h-7" />
          <div className="absolute -top-2 -right-2 bg-accent text-white text-xs rounded-full w-6 h-6 flex items-center justify-center animate-pulse font-bold">
            !
          </div>
        </button>
      )}

      {/* Chat Window */}
      {isOpen && (
        <div className="bg-white rounded-2xl shadow-2xl w-96 h-[500px] flex flex-col border border-gray-100 animate-fade-in-up">
          {/* Header */}
          <div className="bg-gradient-to-r from-primary to-primary-light text-white p-6 rounded-t-2xl flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <FiMessageCircle className="w-5 h-5" />
              </div>
              <div>
                <h3 className="font-bold text-lg">CliniX Support</h3>
                <p className="text-sm text-white/90">We're here to help!</p>
              </div>
            </div>
            <button
              onClick={toggleChat}
              className="text-white hover:text-white/70 transition-colors p-2 hover:bg-white/10 rounded-full"
              aria-label="Close chat"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 p-6 space-y-6 overflow-y-auto custom-scrollbar">
            {/* Welcome Message */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 max-w-xs animate-fade-in-left">
              <p className="text-gray-800 font-medium">
                Hello! 👋 Welcome to CliniX Medical Centre. How can we help you today?
              </p>
            </div>

            {/* Quick Actions */}
            <div className="space-y-3 animate-fade-in-up delay-200">
              <p className="text-sm text-gray-600 font-semibold">Quick actions:</p>
              {quickMessages.map((msg, index) => (
                <button
                  key={index}
                  onClick={() => setMessage(msg)}
                  className={`block w-full text-left bg-blue-50 hover:bg-primary hover:text-white text-primary text-sm p-3 rounded-xl transition-all duration-300 font-medium hover:scale-105 animate-fade-in-up delay-${(index + 3) * 100}`}
                >
                  {msg}
                </button>
              ))}
            </div>

            {/* Call Option */}
            <div className="bg-gradient-to-r from-accent/10 to-accent/20 border border-accent/30 rounded-xl p-4 animate-fade-in-up delay-600">
              <p className="text-accent-dark font-semibold mb-3">
                Need immediate assistance?
              </p>
              <a
                href="tel:+14035450369"
                className="inline-flex items-center bg-gradient-to-r from-accent to-accent-dark text-white px-4 py-3 rounded-xl hover:scale-105 transition-all duration-300 font-semibold shadow-lg"
              >
                <FiPhone className="w-5 h-5 mr-2" />
                Call (*************
              </a>
            </div>
          </div>

          {/* Input */}
          <form onSubmit={handleSubmit} className="p-6 border-t border-gray-100">
            <div className="flex space-x-3">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 border border-gray-200 rounded-xl px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
              />
              <button
                type="submit"
                className="bg-gradient-to-r from-primary to-primary-light text-white p-3 rounded-xl hover:scale-105 transition-all duration-300 shadow-lg"
                aria-label="Send message"
              >
                <FiSend className="w-5 h-5" />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-3 font-medium">
              Messages will connect you with our team during business hours.
            </p>
          </form>
        </div>
      )}
    </div>
  );
}
