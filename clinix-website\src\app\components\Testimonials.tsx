import { FiStar } from 'react-icons/fi';

const testimonials = [
  {
    name: "<PERSON>",
    rating: 5,
    text: "Excellent care and very professional staff. Dr. <PERSON> took the time to listen to my concerns and provided thorough treatment. Highly recommend CliniX!",
    location: "Calgary, AB"
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "Great clinic with convenient hours. The evening appointments work perfectly with my schedule. Clean facility and friendly staff.",
    location: "Calgary, AB"
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "I've been coming here for over a year now. The doctors are knowledgeable and the staff is always helpful. Best medical clinic in the area!",
    location: "Calgary, AB"
  }
];

export default function Testimonials() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="font-heading text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            What Our Patients Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our patients have to say about their experience at CliniX Medical Centre.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-gray-50 rounded-xl p-8 border border-gray-100 hover:shadow-lg transition-shadow"
            >
              {/* Stars */}
              <div className="flex space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <FiStar key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.text}"
              </blockquote>

              {/* Author */}
              <div className="border-t border-gray-200 pt-4">
                <div className="font-semibold text-gray-900">{testimonial.name}</div>
                <div className="text-sm text-gray-600">{testimonial.location}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Google Reviews CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
            <h3 className="font-heading text-2xl font-semibold text-gray-900 mb-4">
              Share Your Experience
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We value your feedback! Help other patients by sharing your experience with CliniX Medical Centre.
            </p>
            <a
              href="https://g.co/kgs/dCwDSoh"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-blue-600 text-white px-8 py-3 rounded-full hover:bg-blue-700 transition-colors font-semibold"
            >
              <FiStar className="w-5 h-5 mr-2" />
              Leave a Google Review
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
