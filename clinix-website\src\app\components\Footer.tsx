import Image from 'next/image';
import Link from 'next/link';
import { FiPhone, FiMail, FiMapPin, FiClock, FiFacebook, FiInstagram, FiTwitter } from 'react-icons/fi';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <Link href="/" className="inline-block">
              <Image
                src="/CliniX-Logo.png"
                alt="CliniX Medical Centre"
                width={160}
                height={50}
                className="h-10 w-auto brightness-0 invert"
              />
            </Link>
            <p className="text-gray-300 leading-relaxed">
              CliniX Medical Centre provides comprehensive healthcare services to the Calgary community. 
              Your health and well-being are our top priorities.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-primary transition-colors"
                aria-label="Facebook"
              >
                <FiFacebook className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-primary transition-colors"
                aria-label="Instagram"
              >
                <FiInstagram className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-primary transition-colors"
                aria-label="Twitter"
              >
                <FiTwitter className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="#services" className="text-gray-300 hover:text-white transition-colors">
                  Our Services
                </Link>
              </li>
              <li>
                <Link href="#about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="#contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <a href="tel:+14035450369" className="text-gray-300 hover:text-white transition-colors">
                  Book Appointment
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Our Services</h3>
            <ul className="space-y-3">
              <li>
                <span className="text-gray-300">Family Medicine</span>
              </li>
              <li>
                <span className="text-gray-300">Walk-in Clinic</span>
              </li>
              <li>
                <span className="text-gray-300">Vaccinations</span>
              </li>
              <li>
                <span className="text-gray-300">Health Assessments</span>
              </li>
              <li>
                <span className="text-gray-300">Specialized Care</span>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <FiMapPin className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-gray-300">
                    1029 17 Ave SW #200<br />
                    Calgary, AB T2T 0A9
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiPhone className="w-5 h-5 text-primary flex-shrink-0" />
                <a href="tel:+14035450369" className="text-gray-300 hover:text-white transition-colors">
                  (*************
                </a>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiMail className="w-5 h-5 text-primary flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              
              <div className="flex items-start space-x-3">
                <FiClock className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>Mon-Wed: 5-7pm</p>
                  <p>Thu: 9am-5pm</p>
                  <p>Fri-Sun: Closed</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} CliniX Medical Centre. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Accessibility
              </Link>
            </div>
          </div>
        </div>

        {/* Emergency Notice */}
        <div className="mt-8 bg-red-900/50 border border-red-800 rounded-lg p-4 text-center">
          <p className="text-red-200 text-sm">
            <strong>Medical Emergency:</strong> Call 911 immediately or visit your nearest emergency room.
          </p>
        </div>
      </div>
    </footer>
  );
}
