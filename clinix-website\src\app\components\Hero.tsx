import Link from 'next/link';
import { FiPhone, FiCalendar, FiClock, FiMapPin } from 'react-icons/fi';

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Enhanced 2025 background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/15 via-purple-600/15 to-teal-600/15"></div>
      <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-blue-500/8 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-[400px] h-[400px] bg-purple-500/8 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-accent/5 rounded-full blur-3xl animate-pulse delay-500"></div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-2 h-2 bg-white/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-32 w-3 h-3 bg-accent/30 rounded-full animate-float delay-300"></div>
        <div className="absolute bottom-32 left-1/4 w-2 h-2 bg-primary/30 rounded-full animate-float delay-700"></div>
        <div className="absolute bottom-20 right-20 w-4 h-4 bg-white/10 rounded-full animate-float delay-1000"></div>
      </div>

      <div className="container-2025 relative z-10">
        <div className="max-w-7xl mx-auto space-y-16">
          {/* Enhanced Main Content */}
          <div className="text-center space-y-12 animate-fade-in-up">
            <div className="space-y-8">
              <h1 className="font-heading text-5xl sm:text-6xl lg:text-8xl xl:text-9xl font-black text-white leading-tight">
                Welcome to{' '}
                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-teal-400 bg-clip-text text-transparent animate-pulse">
                  CliniX
                </span>
                <br />
                <span className="text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-light text-gray-300 block mt-4">
                  Medical Centre
                </span>
              </h1>
              <p className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl text-gray-300 leading-relaxed max-w-5xl mx-auto font-light">
                Exceptional healthcare services in Calgary with modern facilities and experienced professionals
              </p>
            </div>
          </div>

            {/* Enhanced Key Features */}
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 animate-fade-in-up delay-300">
              <div className="card-2025-glass rounded-3xl p-8 border border-white/30 hover:border-accent/50 transition-all duration-500 hover:scale-105 group">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="bg-accent/20 p-4 rounded-2xl group-hover:bg-accent/30 transition-all duration-300">
                    <FiCalendar className="w-8 h-8 text-accent" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-xl">Same Day</h3>
                    <p className="text-white/80 text-lg">Appointments</p>
                  </div>
                </div>
                <p className="text-white/70">Available when you need us most</p>
              </div>
              <div className="card-2025-glass rounded-3xl p-8 border border-white/30 hover:border-primary/50 transition-all duration-500 hover:scale-105 group">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="bg-primary/20 p-4 rounded-2xl group-hover:bg-primary/30 transition-all duration-300">
                    <FiClock className="w-8 h-8 text-primary-light" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-xl">Flexible</h3>
                    <p className="text-white/80 text-lg">Hours</p>
                  </div>
                </div>
                <p className="text-white/70">Evening & weekend availability</p>
              </div>
              <div className="card-2025-glass rounded-3xl p-8 border border-white/30 hover:border-purple-400/50 transition-all duration-500 hover:scale-105 group sm:col-span-2 lg:col-span-1">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="bg-purple-500/20 p-4 rounded-2xl group-hover:bg-purple-500/30 transition-all duration-300">
                    <FiMapPin className="w-8 h-8 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-xl">Central</h3>
                    <p className="text-white/80 text-lg">Location</p>
                  </div>
                </div>
                <p className="text-white/70">Heart of Calgary, easy access</p>
              </div>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-8 justify-center animate-fade-in-up delay-400">
              <Link
                href="tel:+14035450369"
                className="btn-2025 btn-2025-accent btn-2025-lg animate-pulse-hover group"
              >
                <FiPhone className="w-6 h-6 group-hover:animate-bounce" />
                Call (*************
              </Link>
              <Link
                href="#contact"
                className="btn-2025 btn-2025-secondary btn-2025-lg animate-pulse-hover group"
              >
                <FiMapPin className="w-6 h-6 group-hover:animate-bounce" />
                Visit Our Clinic
              </Link>
            </div>

            {/* Business Hours */}
            <div className="card card-glass p-8 animate-fade-in-up delay-500">
              <h3 className="font-heading font-semibold text-white text-xl mb-6">Business Hours</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-white/80 text-lg">Monday - Wednesday:</span>
                  <span className="font-semibold text-white text-lg">5:00 PM - 7:00 PM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/80 text-lg">Thursday:</span>
                  <span className="font-semibold text-white text-lg">9:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/80 text-lg">Friday - Sunday:</span>
                  <span className="font-semibold text-red-300 text-lg">Closed</span>
                </div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative animate-fade-in-right delay-300">
            <div className="relative z-10 animate-float">
              <img
                src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Modern medical facility"
                className="rounded-3xl shadow-2xl w-full h-[600px] object-cover border-4 border-white/20"
              />
              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
            <div className="absolute -bottom-6 -left-6 w-40 h-40 bg-white/10 rounded-full animate-pulse delay-300"></div>
            <div className="absolute top-1/2 -right-4 w-20 h-20 bg-white/10 rounded-full animate-bounce"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
