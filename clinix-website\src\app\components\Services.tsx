import { FiHeart, FiUsers, FiShield, FiActivity, FiEye, FiTrendingUp, FiPhone, FiMapPin } from 'react-icons/fi';

const services = [
  {
    icon: FiHeart,
    title: 'Family Medicine',
    description: 'Comprehensive primary care for patients of all ages, from routine check-ups to chronic disease management.',
    features: ['Annual Physical Exams', 'Preventive Care', 'Chronic Disease Management', 'Health Screenings']
  },
  {
    icon: FiUsers,
    title: 'Walk-in Clinic',
    description: 'No appointment necessary for urgent but non-emergency medical concerns and minor injuries.',
    features: ['Minor Injuries', 'Cold & Flu Treatment', 'Urgent Care', 'Same-Day Service']
  },
  {
    icon: FiShield,
    title: 'Vaccinations',
    description: 'Complete immunization services for children and adults, including travel vaccines.',
    features: ['Childhood Vaccines', 'Adult Immunizations', 'Travel Vaccines', 'Flu Shots']
  },
  {
    icon: FiActivity,
    title: 'Health Assessments',
    description: 'Comprehensive health evaluations and medical examinations for various purposes.',
    features: ['Pre-Employment Medicals', 'Insurance Medicals', 'Fitness Assessments', 'Health Certificates']
  },
  {
    icon: FiEye,
    title: 'Specialized Care',
    description: 'Focused medical services for specific health conditions and specialized treatments.',
    features: ['Diabetes Care', 'Hypertension Management', 'Mental Health Support', 'Women\'s Health']
  },
  {
    icon: FiTrendingUp,
    title: 'Wellness Programs',
    description: 'Proactive health and wellness programs designed to help you maintain optimal health.',
    features: ['Nutrition Counseling', 'Lifestyle Coaching', 'Weight Management', 'Smoking Cessation']
  }
];

export default function Services() {
  return (
    <section id="services" className="section-padding-2025-lg bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/3 rounded-full -translate-x-48 -translate-y-48 blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-accent/3 rounded-full translate-x-64 translate-y-64 blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-purple-500/2 rounded-full blur-3xl"></div>

      <div className="container-2025 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-24 animate-fade-in-up space-y-8">
          <div className="space-y-6">
            <h2 className="font-heading text-5xl lg:text-7xl font-black text-slate-900 mb-8">
              Our Medical{' '}
              <span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
                Services
              </span>
            </h2>
            <p className="text-2xl lg:text-3xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              Comprehensive healthcare services designed to meet all your medical needs with
              professional care and modern facilities.
            </p>
          </div>
          <div className="w-32 h-2 bg-gradient-to-r from-primary to-accent rounded-full mx-auto"></div>
        </div>

        {/* Enhanced Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12 mb-32">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div
                key={index}
                className={`card-2025 card-2025-hover bg-white rounded-3xl p-12 group animate-fade-in-up delay-${(index + 1) * 100} relative overflow-hidden shadow-xl border-2 border-gray-100`}
              >
                {/* Card background gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/3 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10 space-y-8">
                  <div className="space-y-8">
                    <div className="bg-gradient-to-br from-primary to-primary-light w-28 h-28 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-2xl">
                      <IconComponent className="w-14 h-14 text-white" />
                    </div>
                    <h3 className="font-heading text-3xl font-bold text-slate-900 group-hover:text-primary transition-colors duration-300 leading-tight">
                      {service.title}
                    </h3>
                    <p className="text-slate-600 text-xl leading-relaxed">
                      {service.description}
                    </p>
                  </div>

                  <ul className="space-y-5">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-slate-700 group-hover:text-slate-900 transition-colors duration-300">
                        <div className="w-4 h-4 bg-gradient-to-r from-primary to-accent rounded-full mr-6 flex-shrink-0 group-hover:scale-125 transition-transform duration-300 shadow-md"></div>
                        <span className="font-semibold text-lg">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        {/* Enhanced CTA Section */}
        <div className="text-center animate-fade-in-up delay-700">
          <div className="card-2025-elevated bg-gradient-to-br from-primary via-primary-light to-accent rounded-[3rem] p-16 text-white relative overflow-hidden">
            {/* Enhanced Background decorations */}
            <div className="absolute top-0 right-0 w-48 h-48 bg-white/10 rounded-full translate-x-24 -translate-y-24 blur-xl"></div>
            <div className="absolute bottom-0 left-0 w-36 h-36 bg-white/10 rounded-full -translate-x-18 translate-y-18 blur-xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>

            <div className="relative z-10 space-y-10">
              <div className="space-y-6">
                <h3 className="font-heading text-4xl lg:text-6xl font-black mb-8">
                  Need Medical Care?
                </h3>
                <p className="text-2xl lg:text-3xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
                  Our experienced medical team is here to help. Contact us today to schedule an appointment
                  or visit our walk-in clinic for immediate care.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-8 justify-center">
                <a
                  href="tel:+14035450369"
                  className="btn-2025 btn-2025-accent btn-2025-lg animate-pulse-hover group"
                >
                  <FiPhone className="w-6 h-6 group-hover:animate-bounce" />
                  Call (*************
                </a>
                <a
                  href="#contact"
                  className="btn-2025 btn-2025-secondary btn-2025-lg animate-pulse-hover group"
                >
                  <FiMapPin className="w-6 h-6 group-hover:animate-bounce" />
                  Get Directions
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
