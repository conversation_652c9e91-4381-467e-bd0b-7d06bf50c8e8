"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Header.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Header.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiClock,FiMail,FiMapPin,FiMenu,FiPhone,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 text-white py-6 px-6 text-sm animate-slide-in-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-2025 flex flex-col lg:flex-row justify-between items-center gap-6 lg:gap-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center gap-6 sm:gap-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPhone, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg text-white\",\n                                            children: \"(403) 545-0369\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMail, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg text-white\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center gap-6 sm:gap-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMapPin, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg text-white\",\n                                            children: \"1029 17 Ave SW #200, Calgary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiClock, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg text-white\",\n                                            children: \"Mon-Wed 5-7pm, Thu 9am-5pm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/90 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-gray-100/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-2025\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 animate-fade-in-left group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/CliniX-Logo.png\",\n                                                alt: \"CliniX Medical Centre\",\n                                                width: 220,\n                                                height: 80,\n                                                className: \"h-16 w-auto group-hover:scale-105 transition-all duration-500\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-12 animate-fade-in-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"text-slate-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group px-2 py-1\",\n                                            children: [\n                                                \"Home\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-2 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#services\",\n                                            className: \"text-slate-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group px-2 py-1\",\n                                            children: [\n                                                \"Services\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-2 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#about\",\n                                            className: \"text-slate-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group px-2 py-1\",\n                                            children: [\n                                                \"About\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-2 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#contact\",\n                                            className: \"text-slate-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group px-2 py-1\",\n                                            children: [\n                                                \"Contact\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-2 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"tel:+14035450369\",\n                                            className: \"btn-2025 btn-2025-primary btn-2025-lg animate-pulse-hover ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPhone, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Book Appointment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"lg:hidden p-4 rounded-2xl text-slate-700 hover:text-primary hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5 focus:outline-none transition-all duration-300 border border-gray-200 hover:border-primary/20\",\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiX, {\n                                        className: \"w-7 h-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMenu, {\n                                        className: \"w-7 h-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 59\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden py-8 border-t border-gray-200/50 animate-fade-in-up bg-gradient-to-b from-white/95 to-white/90 backdrop-blur-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#services\",\n                                        className: \"text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#about\",\n                                        className: \"text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#contact\",\n                                        className: \"text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"tel:+14035450369\",\n                                        className: \"btn-2025 btn-2025-primary btn-2025-lg text-center mt-6 w-full justify-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiMail_FiMapPin_FiMenu_FiPhone_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPhone, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Book Appointment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\CliniX-Website\\\\clinix-website\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Header.tsx\n"));

/***/ })

});