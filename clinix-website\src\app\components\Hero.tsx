import Link from 'next/link';
import { FiPhone, FiCalendar, FiClock, FiMapPin } from 'react-icons/fi';

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Enhanced 2025 background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/15 via-purple-600/15 to-teal-600/15"></div>
      <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-blue-500/8 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-[400px] h-[400px] bg-purple-500/8 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-accent/5 rounded-full blur-3xl animate-pulse delay-500"></div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-2 h-2 bg-white/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-32 w-3 h-3 bg-accent/30 rounded-full animate-float delay-300"></div>
        <div className="absolute bottom-32 left-1/4 w-2 h-2 bg-primary/30 rounded-full animate-float delay-700"></div>
        <div className="absolute bottom-20 right-20 w-4 h-4 bg-white/10 rounded-full animate-float delay-1000"></div>
      </div>

      <div className="container-2025 relative z-10 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Enhanced Main Content */}
            <div className="text-center lg:text-left space-y-16 animate-fade-in-up px-6 lg:px-0">
              <div className="space-y-12">
                <h1 className="font-heading text-5xl sm:text-6xl lg:text-7xl font-black text-white leading-tight drop-shadow-2xl">
                  Welcome to{' '}
                  <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-teal-400 bg-clip-text text-transparent animate-pulse drop-shadow-lg">
                    CliniX
                  </span>
                  <br />
                  <span className="text-3xl sm:text-4xl lg:text-5xl font-light text-gray-100 block mt-6 drop-shadow-xl">
                    Medical Centre
                  </span>
                </h1>
                <p className="text-xl sm:text-2xl lg:text-3xl text-gray-100 leading-relaxed max-w-4xl mx-auto lg:mx-0 font-light drop-shadow-lg">
                  Exceptional healthcare services in Calgary with modern facilities and experienced professionals
                </p>
              </div>

              {/* Enhanced Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 animate-fade-in-up delay-300">
                <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/30 hover:border-accent/50 transition-all duration-500 hover:scale-105 group shadow-2xl">
                  <div className="flex items-center gap-6 mb-6">
                    <div className="bg-accent/30 p-4 rounded-2xl group-hover:bg-accent/40 transition-all duration-300 shadow-lg">
                      <FiCalendar className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white text-xl drop-shadow-lg">Same Day Appointments</h3>
                    </div>
                  </div>
                  <p className="text-white/90 text-lg drop-shadow-md">Available when you need us most</p>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/30 hover:border-primary/50 transition-all duration-500 hover:scale-105 group shadow-2xl">
                  <div className="flex items-center gap-6 mb-6">
                    <div className="bg-primary/30 p-4 rounded-2xl group-hover:bg-primary/40 transition-all duration-300 shadow-lg">
                      <FiClock className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white text-xl drop-shadow-lg">Flexible Hours</h3>
                    </div>
                  </div>
                  <p className="text-white/90 text-lg drop-shadow-md">Evening & weekend availability</p>
                </div>
              </div>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-8 justify-center lg:justify-start animate-fade-in-up delay-400">
                <Link
                  href="tel:+14035450369"
                  className="btn-2025 btn-2025-accent btn-2025-lg animate-pulse-hover group shadow-2xl"
                >
                  <FiPhone className="w-7 h-7 group-hover:animate-bounce" />
                  Call (*************
                </Link>
                <Link
                  href="#contact"
                  className="btn-2025 btn-2025-secondary btn-2025-lg animate-pulse-hover group shadow-2xl"
                >
                  <FiMapPin className="w-7 h-7 group-hover:animate-bounce" />
                  Visit Our Clinic
                </Link>
              </div>
            </div>

            {/* Enhanced Image */}
            <div className="relative animate-fade-in-right delay-300 px-6 lg:px-0">
              <div className="relative z-10 animate-float">
                <img
                  src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Modern medical facility"
                  className="rounded-3xl shadow-2xl w-full h-[500px] object-cover border-4 border-white/30"
                />
                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-3xl"></div>
              </div>
              {/* Enhanced Decorative elements */}
              <div className="absolute -top-8 -right-8 w-32 h-32 bg-accent/20 rounded-full animate-pulse shadow-xl"></div>
              <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-primary/20 rounded-full animate-pulse delay-300 shadow-xl"></div>
              <div className="absolute top-1/2 -right-6 w-20 h-20 bg-white/20 rounded-full animate-bounce shadow-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
