import { FiPhone, FiMail, FiMapPin, FiClock, FiNavigation } from 'react-icons/fi';

export default function Contact() {
  return (
    <section id="contact" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="font-heading text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Contact & Location
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Visit us at our convenient Calgary location or get in touch to schedule your appointment.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
              <h3 className="font-heading text-2xl font-semibold text-gray-900 mb-6">
                Get In Touch
              </h3>
              
              <div className="space-y-6">
                {/* Phone */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-full">
                    <FiPhone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Phone</h4>
                    <a 
                      href="tel:+14035450369" 
                      className="text-primary hover:text-primary-dark transition-colors text-lg font-medium"
                    >
                      (*************
                    </a>
                    <p className="text-gray-600 text-sm mt-1">Call for appointments and inquiries</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-full">
                    <FiMail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Email</h4>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-primary hover:text-primary-dark transition-colors text-lg font-medium"
                    >
                      <EMAIL>
                    </a>
                    <p className="text-gray-600 text-sm mt-1">Send us your questions or concerns</p>
                  </div>
                </div>

                {/* Address */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-full">
                    <FiMapPin className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Address</h4>
                    <address className="text-gray-700 not-italic">
                      1029 17 Ave SW #200<br />
                      Calgary, AB T2T 0A9<br />
                      Canada
                    </address>
                    <a 
                      href="https://maps.google.com/?q=1029+17+Ave+SW+200,+Calgary,+AB+T2T+0A9"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-primary hover:text-primary-dark transition-colors text-sm mt-2"
                    >
                      <FiNavigation className="w-4 h-4 mr-1" />
                      Get Directions
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-3 mb-6">
                <div className="bg-accent/10 p-3 rounded-full">
                  <FiClock className="w-6 h-6 text-accent" />
                </div>
                <h3 className="font-heading text-2xl font-semibold text-gray-900">
                  Business Hours
                </h3>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Monday</span>
                  <span className="text-gray-900">5:00 PM - 7:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Tuesday</span>
                  <span className="text-gray-900">5:00 PM - 7:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Wednesday</span>
                  <span className="text-gray-900">5:00 PM - 7:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Thursday</span>
                  <span className="text-gray-900">9:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Friday</span>
                  <span className="text-red-600 font-medium">Closed</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Saturday</span>
                  <span className="text-red-600 font-medium">Closed</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-700 font-medium">Sunday</span>
                  <span className="text-red-600 font-medium">Closed</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> For medical emergencies, please call 911 or visit your nearest emergency room.
                </p>
              </div>
            </div>
          </div>

          {/* Map */}
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
            <h3 className="font-heading text-2xl font-semibold text-gray-900 mb-6">
              Find Us
            </h3>
            
            <div className="aspect-video rounded-lg overflow-hidden">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2508.8!2d-114.0719!3d51.0375!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53716f7c1b9b1b1b%3A0x1b1b1b1b1b1b1b1b!2s1029%2017%20Ave%20SW%20%23200%2C%20Calgary%2C%20AB%20T2T%200A9!5e0!3m2!1sen!2sca!4v1234567890"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="CliniX Medical Centre Location"
              ></iframe>
            </div>

            <div className="mt-6 space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">Parking Available</span>
                <span className="text-green-600 font-medium">✓ Free</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">Wheelchair Accessible</span>
                <span className="text-green-600 font-medium">✓ Yes</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="text-gray-700">Public Transit</span>
                <span className="text-green-600 font-medium">✓ Nearby</span>
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Notice */}
        <div className="mt-12 bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <h3 className="font-heading text-xl font-semibold text-red-800 mb-2">
            Medical Emergency?
          </h3>
          <p className="text-red-700 mb-4">
            For life-threatening emergencies, call 911 immediately or visit your nearest emergency room.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="tel:911" 
              className="bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition-colors font-semibold"
            >
              Call 911
            </a>
            <a 
              href="tel:811" 
              className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-semibold"
            >
              Health Link 811
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
