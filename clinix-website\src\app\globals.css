@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #0f172a;
  --primary: #0066cc;
  --primary-dark: #004499;
  --primary-light: #3388dd;
  --secondary: #f8fafc;
  --accent: #00d4aa;
  --accent-dark: #00b894;
  --text-muted: #64748b;
  --text-light: #94a3b8;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --surface: #ffffff;
  --surface-elevated: #f8fafc;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
  --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-accent-dark: var(--accent-dark);
  --color-text-muted: var(--text-muted);
  --color-border: var(--border);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
  --shadow: var(--shadow);
  --shadow-lg: var(--shadow-lg);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

.font-heading {
  font-family: var(--font-poppins), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

/* Modern 2025 Container System */
.container-2025 {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media (min-width: 640px) {
  .container-2025 {
    padding: 0 2rem;
  }
}

@media (min-width: 768px) {
  .container-2025 {
    padding: 0 3rem;
  }
}

@media (min-width: 1024px) {
  .container-2025 {
    padding: 0 4rem;
  }
}

@media (min-width: 1280px) {
  .container-2025 {
    padding: 0 6rem;
  }
}

@media (min-width: 1536px) {
  .container-2025 {
    padding: 0 8rem;
  }
}

/* Legacy container for backward compatibility */
.container-custom {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 2rem;
  }
}

@media (min-width: 768px) {
  .container-custom {
    padding: 0 3rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 4rem;
  }
}

@media (min-width: 1280px) {
  .container-custom {
    padding: 0 6rem;
  }
}

/* Modern 2025 Section Spacing System */
.section-2025 {
  padding: 8rem 0;
}

.section-2025-lg {
  padding: 10rem 0;
}

.section-2025-xl {
  padding: 12rem 0;
}

.section-2025-sm {
  padding: 6rem 0;
}

@media (max-width: 1024px) {
  .section-2025 {
    padding: 6rem 0;
  }

  .section-2025-lg {
    padding: 8rem 0;
  }

  .section-2025-xl {
    padding: 10rem 0;
  }

  .section-2025-sm {
    padding: 4rem 0;
  }
}

@media (max-width: 768px) {
  .section-2025 {
    padding: 4rem 0;
  }

  .section-2025-lg {
    padding: 5rem 0;
  }

  .section-2025-xl {
    padding: 6rem 0;
  }

  .section-2025-sm {
    padding: 3rem 0;
  }
}

/* Legacy section spacing */
.section {
  padding: 6rem 0;
}

.section-large {
  padding: 8rem 0;
}

.section-xl {
  padding: 10rem 0;
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }

  .section-large {
    padding: 5rem 0;
  }

  .section-xl {
    padding: 6rem 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Chat widget positioning */
.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-pulse-hover:hover {
  animation: pulse 0.6s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.6s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Delay classes for staggered animations */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Modern 2025 Button Styles */
.btn-2025 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.25rem;
  border-radius: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  letter-spacing: 0.5px;
  gap: 0.75rem;
}

.btn-2025::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.2), rgba(255,255,255,0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-2025:hover::after {
  opacity: 1;
}

.btn-2025:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.btn-2025-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-2025-primary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-2025-secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: var(--primary);
  border: 1px solid rgba(0, 102, 204, 0.2);
}

.btn-2025-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-2025-outline {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-2025-outline:hover {
  background: rgba(0, 102, 204, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-2025-accent {
  background: var(--gradient-accent);
  color: white;
}

.btn-2025-accent:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-2025-lg {
  padding: 1.25rem 2.75rem;
  font-size: 1.125rem;
  border-radius: 1.25rem;
}

.btn-2025-sm {
  padding: 0.75rem 1.75rem;
  font-size: 0.875rem;
  border-radius: 0.75rem;
}

.btn-2025-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-2025-icon-sm {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
}

.btn-2025-icon-lg {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 1.25rem;
}

/* Legacy button styles for backward compatibility */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn-accent {
  background: linear-gradient(135deg, var(--accent), var(--accent-dark));
  color: white;
}

.btn-accent:hover {
  background: linear-gradient(135deg, var(--accent-dark), var(--accent));
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn-large {
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
}

.btn-small {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

/* Modern 2025 Card Styles */
.card-2025 {
  background: var(--surface);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.card-2025-sm {
  padding: 1.75rem;
  border-radius: 1.25rem;
}

.card-2025-lg {
  padding: 3rem;
  border-radius: 2rem;
}

.card-2025-hover {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-2025-hover:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(0, 102, 204, 0.2);
}

.card-2025-hover:hover::after {
  opacity: 1;
}

.card-2025-hover::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.03) 0%, rgba(0, 102, 204, 0) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.card-2025-glass {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-2025-glass-dark {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(15, 23, 42, 0.3);
  color: white;
}

.card-2025-elevated {
  background: var(--surface);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-lg);
  border: none;
  position: relative;
}

.card-2025-elevated::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1.5rem;
  padding: 2px;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.3) 0%, rgba(0, 212, 170, 0.3) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

/* Legacy card styles for backward compatibility */
.card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border);
}

.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
}

.card-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-accent {
  background: linear-gradient(135deg, var(--accent), var(--accent-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern 2025 Section Padding System */
.section-padding-2025 {
  padding: 8rem 0;
}

.section-padding-2025-lg {
  padding: 10rem 0;
}

.section-padding-2025-xl {
  padding: 12rem 0;
}

.section-padding-2025-sm {
  padding: 6rem 0;
}

.section-padding-2025-xs {
  padding: 4rem 0;
}

@media (max-width: 1024px) {
  .section-padding-2025 {
    padding: 6rem 0;
  }

  .section-padding-2025-lg {
    padding: 8rem 0;
  }

  .section-padding-2025-xl {
    padding: 10rem 0;
  }

  .section-padding-2025-sm {
    padding: 4rem 0;
  }

  .section-padding-2025-xs {
    padding: 3rem 0;
  }
}

@media (max-width: 768px) {
  .section-padding-2025 {
    padding: 4rem 0;
  }

  .section-padding-2025-lg {
    padding: 5rem 0;
  }

  .section-padding-2025-xl {
    padding: 6rem 0;
  }

  .section-padding-2025-sm {
    padding: 3rem 0;
  }

  .section-padding-2025-xs {
    padding: 2rem 0;
  }
}

/* Legacy section padding */
.section-padding {
  padding: 5rem 0;
}

.section-padding-large {
  padding: 8rem 0;
}

.section-padding-small {
  padding: 3rem 0;
}

/* Modern spacing utilities */
.space-y-2025 > * + * {
  margin-top: 3rem;
}

.space-y-2025-lg > * + * {
  margin-top: 4rem;
}

.space-y-2025-xl > * + * {
  margin-top: 6rem;
}

.space-y-2025-sm > * + * {
  margin-top: 2rem;
}

.gap-2025 {
  gap: 3rem;
}

.gap-2025-lg {
  gap: 4rem;
}

.gap-2025-xl {
  gap: 6rem;
}

.gap-2025-sm {
  gap: 2rem;
}

@media (max-width: 768px) {
  .space-y-2025 > * + * {
    margin-top: 2rem;
  }

  .space-y-2025-lg > * + * {
    margin-top: 3rem;
  }

  .space-y-2025-xl > * + * {
    margin-top: 4rem;
  }

  .space-y-2025-sm > * + * {
    margin-top: 1.5rem;
  }

  .gap-2025 {
    gap: 2rem;
  }

  .gap-2025-lg {
    gap: 3rem;
  }

  .gap-2025-xl {
    gap: 4rem;
  }

  .gap-2025-sm {
    gap: 1.5rem;
  }
}

/* Hero section styles */
.hero-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--accent) 100%);
}

.hero-overlay {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(16, 185, 129, 0.9) 100%);
}

/* Custom scrollbar for webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}
