'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { FiMenu, FiX, FiPhone, FiMail, FiMapPin, FiClock } from 'react-icons/fi';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <>
      {/* Modern 2025 Top Bar */}
      <div className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 text-white py-6 px-6 text-sm animate-slide-in-top">
        <div className="container-2025 flex flex-col lg:flex-row justify-between items-center gap-6 lg:gap-0">
          <div className="flex flex-col sm:flex-row items-center gap-6 sm:gap-10">
            <div className="flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5">
              <div className="bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg">
                <FiPhone className="w-5 h-5" />
              </div>
              <span className="font-bold text-lg text-white">(*************</span>
            </div>
            <div className="flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5">
              <div className="bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg">
                <FiMail className="w-5 h-5" />
              </div>
              <span className="font-bold text-lg text-white"><EMAIL></span>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row items-center gap-6 sm:gap-10">
            <div className="flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5">
              <div className="bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg">
                <FiMapPin className="w-5 h-5" />
              </div>
              <span className="font-bold text-lg text-white">1029 17 Ave SW #200, Calgary</span>
            </div>
            <div className="flex items-center gap-4 hover:text-accent transition-all duration-300 group px-4 py-2 rounded-xl hover:bg-white/5">
              <div className="bg-white/15 p-3 rounded-xl group-hover:bg-accent/25 transition-all duration-300 shadow-lg">
                <FiClock className="w-5 h-5" />
              </div>
              <span className="font-bold text-lg text-white">Mon-Wed 5-7pm, Thu 9am-5pm</span>
            </div>
          </div>
        </div>
      </div>

      {/* Modern 2025 Main Header */}
      <header className="bg-white/95 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-gray-100/50">
        <div className="container-2025">
          <div className="flex justify-between items-center py-10">
            {/* Enhanced Logo */}
            <Link href="/" className="flex items-center gap-6 animate-fade-in-left group p-4 rounded-2xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5 transition-all duration-500">
              <div className="relative">
                <Image
                  src="/CliniX-Logo.png"
                  alt="CliniX Medical Centre"
                  width={240}
                  height={90}
                  className="h-18 w-auto group-hover:scale-105 transition-all duration-500"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
              </div>
            </Link>

            {/* Enhanced Desktop Navigation */}
            <nav className="hidden lg:flex items-center gap-16 animate-fade-in-right">
              <Link href="/" className="text-slate-800 hover:text-primary font-bold text-xl transition-all duration-300 hover:scale-105 relative group px-4 py-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5">
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#services" className="text-slate-800 hover:text-primary font-bold text-xl transition-all duration-300 hover:scale-105 relative group px-4 py-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5">
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#about" className="text-slate-800 hover:text-primary font-bold text-xl transition-all duration-300 hover:scale-105 relative group px-4 py-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5">
                About
                <span className="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#contact" className="text-slate-800 hover:text-primary font-bold text-xl transition-all duration-300 hover:scale-105 relative group px-4 py-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5">
                Contact
                <span className="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link
                href="tel:+14035450369"
                className="btn-2025 btn-2025-primary btn-2025-lg animate-pulse-hover ml-8"
              >
                <FiPhone className="w-6 h-6" />
                Book Appointment
              </Link>
            </nav>

            {/* Enhanced Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden p-4 rounded-2xl text-slate-700 hover:text-primary hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5 focus:outline-none transition-all duration-300 border border-gray-200 hover:border-primary/20"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <FiX className="w-7 h-7" /> : <FiMenu className="w-7 h-7" />}
            </button>
          </div>

          {/* Enhanced Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden py-8 border-t border-gray-200/50 animate-fade-in-up bg-gradient-to-b from-white/95 to-white/90 backdrop-blur-xl">
              <nav className="flex flex-col space-y-8">
                <Link
                  href="/"
                  className="text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </Link>
                <Link
                  href="#services"
                  className="text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Services
                </Link>
                <Link
                  href="#about"
                  className="text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5"
                  onClick={() => setIsMenuOpen(false)}
                >
                  About
                </Link>
                <Link
                  href="#contact"
                  className="text-slate-700 hover:text-primary font-semibold text-xl transition-all duration-300 hover:translate-x-3 hover:scale-105 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Contact
                </Link>
                <Link
                  href="tel:+14035450369"
                  className="btn-2025 btn-2025-primary btn-2025-lg text-center mt-6 w-full justify-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FiPhone className="w-5 h-5" />
                  Book Appointment
                </Link>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
}
