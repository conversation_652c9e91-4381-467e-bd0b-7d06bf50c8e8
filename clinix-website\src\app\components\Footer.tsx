import Image from 'next/image';
import Link from 'next/link';
import { FiPhone, FiMail, FiMapPin, FiClock, FiFacebook, FiInstagram, FiTwitter } from 'react-icons/fi';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full -translate-x-48 -translate-y-48 blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-accent/5 rounded-full translate-x-64 translate-y-64 blur-3xl"></div>

      <div className="container-2025 relative z-10 py-20">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-2025">
          {/* Enhanced Company Info */}
          <div className="space-y-8">
            <Link href="/" className="inline-block group">
              <Image
                src="/CliniX-Logo.png"
                alt="CliniX Medical Centre"
                width={180}
                height={60}
                className="h-12 w-auto brightness-0 invert group-hover:scale-105 transition-transform duration-300"
              />
            </Link>
            <p className="text-gray-300 leading-relaxed text-lg">
              CliniX Medical Centre provides comprehensive healthcare services to the Calgary community.
              Your health and well-being are our top priorities.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="bg-white/10 p-3 rounded-2xl hover:bg-primary/20 hover:scale-110 transition-all duration-300 group"
                aria-label="Facebook"
              >
                <FiFacebook className="w-6 h-6 group-hover:text-primary transition-colors" />
              </a>
              <a
                href="#"
                className="bg-white/10 p-3 rounded-2xl hover:bg-accent/20 hover:scale-110 transition-all duration-300 group"
                aria-label="Instagram"
              >
                <FiInstagram className="w-6 h-6 group-hover:text-accent transition-colors" />
              </a>
              <a
                href="#"
                className="bg-white/10 p-3 rounded-2xl hover:bg-blue-400/20 hover:scale-110 transition-all duration-300 group"
                aria-label="Twitter"
              >
                <FiTwitter className="w-6 h-6 group-hover:text-blue-400 transition-colors" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="#services" className="text-gray-300 hover:text-white transition-colors">
                  Our Services
                </Link>
              </li>
              <li>
                <Link href="#about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="#contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <a href="tel:+14035450369" className="text-gray-300 hover:text-white transition-colors">
                  Book Appointment
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Our Services</h3>
            <ul className="space-y-3">
              <li>
                <span className="text-gray-300">Family Medicine</span>
              </li>
              <li>
                <span className="text-gray-300">Walk-in Clinic</span>
              </li>
              <li>
                <span className="text-gray-300">Vaccinations</span>
              </li>
              <li>
                <span className="text-gray-300">Health Assessments</span>
              </li>
              <li>
                <span className="text-gray-300">Specialized Care</span>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-heading font-semibold text-lg mb-6">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <FiMapPin className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-gray-300">
                    1029 17 Ave SW #200<br />
                    Calgary, AB T2T 0A9
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiPhone className="w-5 h-5 text-primary flex-shrink-0" />
                <a href="tel:+14035450369" className="text-gray-300 hover:text-white transition-colors">
                  (*************
                </a>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiMail className="w-5 h-5 text-primary flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              
              <div className="flex items-start space-x-3">
                <FiClock className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>Mon-Wed: 5-7pm</p>
                  <p>Thu: 9am-5pm</p>
                  <p>Fri-Sun: Closed</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} CliniX Medical Centre. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                Accessibility
              </Link>
            </div>
          </div>
        </div>

        {/* Emergency Notice */}
        <div className="mt-8 bg-red-900/50 border border-red-800 rounded-lg p-4 text-center">
          <p className="text-red-200 text-sm">
            <strong>Medical Emergency:</strong> Call 911 immediately or visit your nearest emergency room.
          </p>
        </div>
      </div>
    </footer>
  );
}
