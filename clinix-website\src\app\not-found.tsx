import Link from 'next/link';
import { FiHome, FiPhone } from 'react-icons/fi';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="text-center max-w-md">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-primary/20">404</h1>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href="/"
            className="inline-flex items-center bg-primary text-white px-6 py-3 rounded-full hover:bg-primary-dark transition-colors font-semibold"
          >
            <FiHome className="w-5 h-5 mr-2" />
            Go Home
          </Link>
          
          <div className="text-gray-500">or</div>
          
          <a
            href="tel:+14035450369"
            className="inline-flex items-center bg-gray-100 text-gray-900 px-6 py-3 rounded-full hover:bg-gray-200 transition-colors font-semibold"
          >
            <FiPhone className="w-5 h-5 mr-2" />
            Call (*************
          </a>
        </div>

        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-100">
          <h3 className="font-semibold text-gray-900 mb-2">Need Medical Assistance?</h3>
          <p className="text-sm text-gray-600 mb-4">
            If this is a medical emergency, please call 911 immediately.
          </p>
          <div className="flex justify-center space-x-4 text-sm">
            <a href="tel:911" className="text-red-600 hover:text-red-700 font-medium">
              Emergency: 911
            </a>
            <a href="tel:811" className="text-blue-600 hover:text-blue-700 font-medium">
              Health Link: 811
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
