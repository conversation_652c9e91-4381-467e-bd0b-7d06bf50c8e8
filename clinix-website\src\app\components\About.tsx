import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fi<PERSON>sers, FiAward } from 'react-icons/fi';

const values = [
  {
    icon: FiTarget,
    title: 'Excellence in Care',
    description: 'We strive to provide the highest quality medical care with attention to detail and patient safety.'
  },
  {
    icon: <PERSON>Hear<PERSON>,
    title: 'Compassionate Service',
    description: 'Every patient is treated with empathy, respect, and understanding in a welcoming environment.'
  },
  {
    icon: FiUsers,
    title: 'Community Focus',
    description: 'We are committed to serving our Calgary community and building lasting relationships with our patients.'
  },
  {
    icon: FiAward,
    title: 'Professional Standards',
    description: 'Our team maintains the highest professional standards and continues ongoing medical education.'
  }
];

const stats = [
  { number: '5+', label: 'Years Serving Calgary' },
  { number: '1000+', label: 'Patients Served' },
  { number: '24/7', label: 'Emergency Support' },
  { number: '100%', label: 'Patient Satisfaction' }
];

export default function About() {
  return (
    <section id="about" className="section-padding-2025-lg bg-gradient-to-br from-white via-slate-50 to-white relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-accent/3 rounded-full translate-x-48 -translate-y-48 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-primary/3 rounded-full -translate-x-64 translate-y-64 blur-3xl"></div>

      <div className="container-2025 relative z-10">
        {/* Enhanced Main About Section */}
        <div className="grid lg:grid-cols-2 gap-20 items-center mb-32">
          {/* Enhanced Content */}
          <div className="space-y-12 animate-fade-in-left px-6 lg:px-0">
            <div className="space-y-12">
              <div className="space-y-8">
                <h2 className="font-heading text-5xl lg:text-7xl font-black text-slate-900 leading-tight">
                  About{' '}
                  <span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
                    CliniX
                  </span>
                  <br />
                  <span className="text-4xl lg:text-5xl font-light text-slate-700">Medical Centre</span>
                </h2>
                <div className="w-32 h-2 bg-gradient-to-r from-primary to-accent rounded-full"></div>
              </div>
              <p className="text-2xl lg:text-3xl text-slate-600 leading-relaxed font-light">
                CliniX Medical Centre is dedicated to providing exceptional healthcare services
                to the Calgary community. Our modern facility and experienced medical team ensure
                you receive the best possible care.
              </p>
              <p className="text-xl text-slate-600 leading-relaxed">
                Located in the heart of Calgary at 1029 17 Ave SW, we offer comprehensive family
                medicine services, walk-in clinic care, and specialized treatments. Our patient-centered
                approach means we take the time to listen to your concerns and develop personalized
                treatment plans that work for you.
              </p>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-2 gap-8 pt-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center group">
                  <div className="card-2025-glass rounded-2xl p-6 border border-primary/10 group-hover:border-primary/30 transition-all duration-300 group-hover:scale-105">
                    <div className="text-4xl lg:text-5xl font-black bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-3">{stat.number}</div>
                    <div className="text-lg font-semibold text-slate-700">{stat.label}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Image */}
          <div className="relative animate-fade-in-right">
            <div className="relative group">
              <img
                src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Medical professionals"
                className="rounded-3xl shadow-2xl w-full h-[500px] object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 via-transparent to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
            <div className="absolute -bottom-8 -left-8 card-2025-elevated bg-white p-8 rounded-3xl shadow-2xl">
              <div className="text-4xl font-black bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">5+</div>
              <div className="text-lg font-semibold text-slate-700">Years of Excellence</div>
            </div>
            {/* Floating decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full animate-pulse"></div>
            <div className="absolute top-1/2 -right-4 w-16 h-16 bg-primary/20 rounded-full animate-bounce"></div>
          </div>
        </div>

        {/* Mission & Values */}
        <div className="space-y-16">
          {/* Mission Statement */}
          <div className="text-center max-w-4xl mx-auto">
            <h3 className="font-heading text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
              Our Mission
            </h3>
            <p className="text-xl text-gray-600 leading-relaxed">
              To provide high-quality, accessible healthcare services that exceed our patients' 
              expectations while fostering a supportive and caring environment for healing and wellness.
            </p>
          </div>

          {/* Values Grid */}
          <div>
            <h3 className="font-heading text-2xl lg:text-3xl font-bold text-gray-900 text-center mb-12">
              Our Values
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => {
                const IconComponent = value.icon;
                return (
                  <div key={index} className="text-center group">
                    <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="w-8 h-8 text-primary" />
                    </div>
                    <h4 className="font-heading font-semibold text-gray-900 mb-3">
                      {value.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {value.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Why Choose Us */}
        <div className="mt-20 bg-gradient-to-r from-primary to-primary-dark rounded-2xl p-8 lg:p-12 text-white">
          <div className="text-center mb-8">
            <h3 className="font-heading text-2xl lg:text-3xl font-bold mb-4">
              Why Choose CliniX Medical Centre?
            </h3>
            <p className="text-blue-100 text-lg max-w-3xl mx-auto">
              We combine modern medical technology with personalized care to deliver 
              exceptional healthcare experiences.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <h4 className="font-semibold text-lg mb-2">Experienced Team</h4>
              <p className="text-blue-100 text-sm">Qualified medical professionals with years of experience</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">Modern Facility</h4>
              <p className="text-blue-100 text-sm">State-of-the-art equipment and comfortable environment</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">Patient-Centered</h4>
              <p className="text-blue-100 text-sm">Personalized care tailored to your individual needs</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
