import { FiStar } from 'react-icons/fi';

const testimonials = [
  {
    name: "<PERSON>",
    rating: 5,
    text: "Excellent care and very professional staff. Dr. <PERSON> took the time to listen to my concerns and provided thorough treatment. Highly recommend CliniX!",
    location: "Calgary, AB"
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "Great clinic with convenient hours. The evening appointments work perfectly with my schedule. Clean facility and friendly staff.",
    location: "Calgary, AB"
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "I've been coming here for over a year now. The doctors are knowledgeable and the staff is always helpful. Best medical clinic in the area!",
    location: "Calgary, AB"
  }
];

export default function Testimonials() {
  return (
    <section className="section-padding-2025-lg bg-gradient-to-br from-white via-slate-50 to-white relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-accent/3 rounded-full translate-x-48 -translate-y-48 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-primary/3 rounded-full -translate-x-64 translate-y-64 blur-3xl"></div>

      <div className="container-2025 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-24 animate-fade-in-up space-y-8">
          <div className="space-y-6">
            <h2 className="font-heading text-5xl lg:text-7xl font-black text-slate-900">
              What Our{' '}
              <span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
                Patients
              </span>{' '}
              Say
            </h2>
            <p className="text-2xl lg:text-3xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
              Don't just take our word for it. Here's what our patients have to say about their experience at CliniX Medical Centre.
            </p>
          </div>
          <div className="w-32 h-2 bg-gradient-to-r from-primary to-accent rounded-full mx-auto"></div>
        </div>

        {/* Enhanced Testimonials Grid */}
        <div className="grid md:grid-cols-3 gap-2025 mb-20">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="card-2025 card-2025-hover bg-white rounded-3xl p-10 border border-gray-100/50 relative overflow-hidden group"
            >
              {/* Card background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/2 via-transparent to-accent/2 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative z-10">
                {/* Enhanced Stars */}
                <div className="flex space-x-2 mb-8">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <div key={i} className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-1 rounded-full">
                      <FiStar className="w-6 h-6 text-white fill-current" />
                    </div>
                  ))}
                </div>

                {/* Enhanced Quote */}
                <blockquote className="text-slate-700 mb-8 leading-relaxed text-xl font-medium">
                  "{testimonial.text}"
                </blockquote>

                {/* Enhanced Author */}
                <div className="border-t border-gray-200/50 pt-6">
                  <div className="font-bold text-slate-900 text-xl">{testimonial.name}</div>
                  <div className="text-lg text-slate-600 font-medium">{testimonial.location}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Google Reviews CTA */}
        <div className="text-center">
          <div className="card-2025-elevated bg-gradient-to-br from-primary via-primary-light to-accent rounded-[3rem] p-16 text-white relative overflow-hidden">
            {/* Enhanced Background decorations */}
            <div className="absolute top-0 right-0 w-48 h-48 bg-white/10 rounded-full translate-x-24 -translate-y-24 blur-xl"></div>
            <div className="absolute bottom-0 left-0 w-36 h-36 bg-white/10 rounded-full -translate-x-18 translate-y-18 blur-xl"></div>

            <div className="relative z-10 space-y-8">
              <div className="space-y-6">
                <h3 className="font-heading text-4xl lg:text-6xl font-black">
                  Share Your Experience
                </h3>
                <p className="text-2xl lg:text-3xl text-white/90 max-w-3xl mx-auto leading-relaxed font-light">
                  We value your feedback! Help other patients by sharing your experience with CliniX Medical Centre.
                </p>
              </div>
              <a
                href="https://g.co/kgs/dCwDSoh"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-2025 btn-2025-accent btn-2025-lg animate-pulse-hover group inline-flex"
              >
                <FiStar className="w-6 h-6 group-hover:animate-bounce" />
                Leave a Google Review
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
