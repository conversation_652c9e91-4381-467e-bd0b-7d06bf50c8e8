'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { FiMenu, FiX, FiPhone, FiMail, FiMapPin } from 'react-icons/fi';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <>
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-primary to-primary-light text-white py-3 px-4 text-sm animate-slide-in-top">
        <div className="container-custom flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-8">
            <div className="flex items-center space-x-2 hover:text-accent transition-colors">
              <FiPhone className="w-4 h-4" />
              <span className="font-medium">(*************</span>
            </div>
            <div className="flex items-center space-x-2 hover:text-accent transition-colors">
              <FiMail className="w-4 h-4" />
              <span className="font-medium"><EMAIL></span>
            </div>
            <div className="flex items-center space-x-2 hover:text-accent transition-colors">
              <FiMapPin className="w-4 h-4" />
              <span className="font-medium">1029 17 Ave SW #200, Calgary, AB</span>
            </div>
          </div>
          <div className="text-center sm:text-right">
            <span className="font-semibold text-white/90">Mon-Wed: 5-7pm | Thu: 9am-5pm</span>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white/95 backdrop-blur-md shadow-xl sticky top-0 z-50 border-b border-gray-100">
        <div className="container-custom">
          <div className="flex justify-between items-center py-6">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 animate-fade-in-left">
              <Image
                src="/CliniX-Logo.png"
                alt="CliniX Medical Centre"
                width={200}
                height={70}
                className="h-14 w-auto hover:scale-105 transition-transform duration-300"
                priority
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-10 animate-fade-in-right">
              <Link href="/" className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group">
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#services" className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group">
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#about" className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group">
                About
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link href="#contact" className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:scale-105 relative group">
                Contact
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              <Link
                href="tel:+14035450369"
                className="btn btn-primary animate-pulse-hover"
              >
                Book Appointment
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-3 rounded-xl text-gray-700 hover:text-primary hover:bg-gray-100 focus:outline-none transition-all duration-300"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <FiX className="w-7 h-7" /> : <FiMenu className="w-7 h-7" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-6 border-t border-gray-200 animate-fade-in-up">
              <nav className="flex flex-col space-y-6">
                <Link
                  href="/"
                  className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:translate-x-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </Link>
                <Link
                  href="#services"
                  className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:translate-x-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Services
                </Link>
                <Link
                  href="#about"
                  className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:translate-x-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  About
                </Link>
                <Link
                  href="#contact"
                  className="text-gray-700 hover:text-primary font-semibold text-lg transition-all duration-300 hover:translate-x-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Contact
                </Link>
                <Link
                  href="tel:+14035450369"
                  className="btn btn-primary text-center mt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Book Appointment
                </Link>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
}
