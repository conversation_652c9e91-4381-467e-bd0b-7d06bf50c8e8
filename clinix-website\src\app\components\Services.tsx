import { FiHeart, FiUsers, FiShield, FiActivity, FiEye, FiTrendingUp } from 'react-icons/fi';

const services = [
  {
    icon: FiHeart,
    title: 'Family Medicine',
    description: 'Comprehensive primary care for patients of all ages, from routine check-ups to chronic disease management.',
    features: ['Annual Physical Exams', 'Preventive Care', 'Chronic Disease Management', 'Health Screenings']
  },
  {
    icon: FiUsers,
    title: 'Walk-in Clinic',
    description: 'No appointment necessary for urgent but non-emergency medical concerns and minor injuries.',
    features: ['Minor Injuries', 'Cold & Flu Treatment', 'Urgent Care', 'Same-Day Service']
  },
  {
    icon: FiShield,
    title: 'Vaccinations',
    description: 'Complete immunization services for children and adults, including travel vaccines.',
    features: ['Childhood Vaccines', 'Adult Immunizations', 'Travel Vaccines', 'Flu Shots']
  },
  {
    icon: FiActivity,
    title: 'Health Assessments',
    description: 'Comprehensive health evaluations and medical examinations for various purposes.',
    features: ['Pre-Employment Medicals', 'Insurance Medicals', 'Fitness Assessments', 'Health Certificates']
  },
  {
    icon: FiEye,
    title: 'Specialized Care',
    description: 'Focused medical services for specific health conditions and specialized treatments.',
    features: ['Diabetes Care', 'Hypertension Management', 'Mental Health Support', 'Women\'s Health']
  },
  {
    icon: FiTrendingUp,
    title: 'Wellness Programs',
    description: 'Proactive health and wellness programs designed to help you maintain optimal health.',
    features: ['Nutrition Counseling', 'Lifestyle Coaching', 'Weight Management', 'Smoking Cessation']
  }
];

export default function Services() {
  return (
    <section id="services" className="section-padding bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-primary/5 rounded-full -translate-x-32 -translate-y-32"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/5 rounded-full translate-x-48 translate-y-48"></div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="font-heading text-4xl lg:text-6xl font-bold gradient-text mb-6">
            Our Medical Services
          </h2>
          <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            We provide comprehensive healthcare services to meet all your medical needs.
            Our experienced team is dedicated to delivering quality care in a comfortable environment.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div
                key={index}
                className={`card card-hover bg-white rounded-2xl p-8 group animate-fade-in-up delay-${(index + 1) * 100}`}
              >
                <div className="mb-8">
                  <div className="bg-gradient-to-br from-primary to-primary-light w-20 h-20 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <IconComponent className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="font-heading text-2xl font-bold text-gray-900 mb-4 group-hover:text-primary transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-6 text-lg leading-relaxed">
                    {service.description}
                  </p>
                </div>

                <ul className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-700">
                      <div className="w-2 h-2 bg-gradient-to-r from-primary to-accent rounded-full mr-4 flex-shrink-0"></div>
                      <span className="font-medium">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center animate-fade-in-up delay-700">
          <div className="card bg-gradient-to-r from-primary to-primary-light rounded-3xl p-12 text-white relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -translate-x-12 translate-y-12"></div>

            <div className="relative z-10">
              <h3 className="font-heading text-3xl lg:text-4xl font-bold mb-6">
                Need Medical Care?
              </h3>
              <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
                Our experienced medical team is here to help. Contact us today to schedule an appointment
                or visit our walk-in clinic for immediate care.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <a
                  href="tel:+14035450369"
                  className="btn btn-accent btn-large animate-pulse-hover"
                >
                  Call (*************
                </a>
                <a
                  href="#contact"
                  className="btn btn-secondary btn-large animate-pulse-hover"
                  style={{ background: 'rgba(255, 255, 255, 0.1)', color: 'white', borderColor: 'white' }}
                >
                  Get Directions
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
